import React from 'react';
import { Shield, ShieldCheck, ShieldX, Lock, Unlock } from 'lucide-react';
import { useEncryption } from '../../contexts/EncryptionContext';

const EncryptionStatus = ({ recipientUserId, className = "" }) => {
  const { isEnabled, isInitialized, isLoading } = useEncryption();

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 text-gray-400 ${className}`}>
        <div className="animate-spin">
          <Shield className="w-4 h-4" />
        </div>
        <span className="text-sm">Setting up encryption...</span>
      </div>
    );
  }

  if (!isInitialized || !isEnabled) {
    return (
      <div className={`flex items-center gap-2 text-yellow-400 ${className}`}>
        <ShieldX className="w-4 h-4" />
        <span className="text-sm">Encryption not available</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 text-green-400 ${className}`}>
      <ShieldCheck className="w-4 h-4" />
      <span className="text-sm">End-to-end encrypted</span>
    </div>
  );
};

const MessageEncryptionIndicator = ({ isEncrypted, className = "" }) => {
  if (isEncrypted) {
    return (
      <div className={`flex items-center gap-1 text-green-400 ${className}`}>
        <Lock className="w-3 h-3" />
        <span className="text-xs">Encrypted</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-1 text-gray-400 ${className}`}>
      <Unlock className="w-3 h-3" />
      <span className="text-xs">Plain text</span>
    </div>
  );
};

export { EncryptionStatus, MessageEncryptionIndicator };
