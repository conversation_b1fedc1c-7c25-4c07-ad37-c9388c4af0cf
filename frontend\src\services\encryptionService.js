import nacl from 'tweetnacl';
import { encodeBase64, decodeBase64, encodeUTF8, decodeUTF8 } from 'tweetnacl-util';

class EncryptionService {
  constructor() {
    this.keyPair = null;
    this.publicKey = null;
    this.privateKey = null;
  }

  /**
   * Initialize encryption service for the current user
   * Generates new key pair if none exists, or loads existing keys
   */
  async initialize() {
    try {
      console.log('🔐 ENCRYPTION SERVICE: Initialize called');

      // Try to load existing keys from localStorage
      const storedPrivateKey = localStorage.getItem('chainverdict_private_key');
      const storedPublicKey = localStorage.getItem('chainverdict_public_key');

      console.log('🔐 ENCRYPTION SERVICE: Stored keys check', {
        hasPrivateKey: !!storedPrivateKey,
        hasPublicKey: !!storedPublicKey
      });

      if (storedPrivateKey && storedPublicKey) {
        // Load existing keys
        this.privateKey = decodeBase64(storedPrivateKey);
        this.publicKey = decodeBase64(storedPublicKey);
        this.keyPair = {
          publicKey: this.publicKey,
          secretKey: this.privateKey
        };
        console.log('🔑 Loaded existing encryption keys');
      } else {
        // Generate new key pair
        await this.generateKeyPair();
        console.log('🔑 Generated new encryption keys');
      }

      return {
        success: true,
        publicKey: encodeBase64(this.publicKey)
      };
    } catch (error) {
      console.error('❌ Failed to initialize encryption:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate a new key pair for the user
   */
  async generateKeyPair() {
    try {
      // Generate new key pair
      this.keyPair = nacl.box.keyPair();
      this.publicKey = this.keyPair.publicKey;
      this.privateKey = this.keyPair.secretKey;

      // Store keys in localStorage
      localStorage.setItem('chainverdict_private_key', encodeBase64(this.privateKey));
      localStorage.setItem('chainverdict_public_key', encodeBase64(this.publicKey));

      console.log('🔑 Generated and stored new key pair');
      return {
        success: true,
        publicKey: encodeBase64(this.publicKey)
      };
    } catch (error) {
      console.error('❌ Failed to generate key pair:', error);
      throw error;
    }
  }

  /**
   * Get the user's public key in base64 format
   */
  getPublicKey() {
    if (!this.publicKey) {
      throw new Error('Encryption not initialized. Call initialize() first.');
    }
    return encodeBase64(this.publicKey);
  }

  /**
   * Encrypt a message for a specific recipient
   * @param {string} message - The message to encrypt
   * @param {string} recipientPublicKey - Recipient's public key in base64
   * @returns {object} Encrypted message data
   */
  encryptMessage(message, recipientPublicKey) {
    try {
      if (!this.privateKey) {
        throw new Error('Private key not available. Initialize encryption first.');
      }

      // Convert recipient's public key from base64
      const recipientPubKey = decodeBase64(recipientPublicKey);
      
      // Generate random nonce
      const nonce = nacl.randomBytes(24);
      
      // Convert message to bytes
      const messageBytes = encodeUTF8(message);
      
      // Encrypt the message
      const encrypted = nacl.box(messageBytes, nonce, recipientPubKey, this.privateKey);
      
      if (!encrypted) {
        throw new Error('Failed to encrypt message');
      }

      return {
        encrypted: encodeBase64(encrypted),
        nonce: encodeBase64(nonce),
        senderPublicKey: encodeBase64(this.publicKey)
      };
    } catch (error) {
      console.error('❌ Failed to encrypt message:', error);
      throw error;
    }
  }

  /**
   * Decrypt a message from a specific sender
   * @param {object} encryptedData - Encrypted message data
   * @param {string} senderPublicKey - Sender's public key in base64
   * @returns {string} Decrypted message
   */
  decryptMessage(encryptedData, senderPublicKey) {
    try {
      if (!this.privateKey) {
        throw new Error('Private key not available. Initialize encryption first.');
      }

      // Convert from base64
      const encrypted = decodeBase64(encryptedData.encrypted);
      const nonce = decodeBase64(encryptedData.nonce);
      const senderPubKey = decodeBase64(senderPublicKey);
      
      // Decrypt the message
      const decrypted = nacl.box.open(encrypted, nonce, senderPubKey, this.privateKey);
      
      if (!decrypted) {
        throw new Error('Failed to decrypt message - invalid keys or corrupted data');
      }

      return decodeUTF8(decrypted);
    } catch (error) {
      console.error('❌ Failed to decrypt message:', error);
      throw error;
    }
  }

  /**
   * Export keys for backup (encrypted with password)
   * @param {string} password - Password to encrypt the backup
   * @returns {string} Encrypted backup data
   */
  exportKeys(password) {
    try {
      if (!this.privateKey || !this.publicKey) {
        throw new Error('No keys to export');
      }

      const keyData = {
        publicKey: encodeBase64(this.publicKey),
        privateKey: encodeBase64(this.privateKey),
        timestamp: Date.now()
      };

      // Simple password-based encryption for backup
      // In production, use more robust key derivation
      const passwordBytes = encodeUTF8(password);
      const nonce = nacl.randomBytes(24);
      const keyBytes = encodeUTF8(JSON.stringify(keyData));
      
      // Use password as key (simplified - should use PBKDF2 in production)
      const key = nacl.hash(passwordBytes).slice(0, 32);
      const encrypted = nacl.secretbox(keyBytes, nonce, key);

      return encodeBase64(encrypted) + '.' + encodeBase64(nonce);
    } catch (error) {
      console.error('❌ Failed to export keys:', error);
      throw error;
    }
  }

  /**
   * Import keys from backup
   * @param {string} backupData - Encrypted backup data
   * @param {string} password - Password to decrypt the backup
   * @returns {boolean} Success status
   */
  importKeys(backupData, password) {
    try {
      const [encryptedData, nonceData] = backupData.split('.');
      const encrypted = decodeBase64(encryptedData);
      const nonce = decodeBase64(nonceData);
      
      const passwordBytes = encodeUTF8(password);
      const key = nacl.hash(passwordBytes).slice(0, 32);
      
      const decrypted = nacl.secretbox.open(encrypted, nonce, key);
      if (!decrypted) {
        throw new Error('Invalid password or corrupted backup');
      }

      const keyData = JSON.parse(decodeUTF8(decrypted));
      
      // Restore keys
      this.publicKey = decodeBase64(keyData.publicKey);
      this.privateKey = decodeBase64(keyData.privateKey);
      this.keyPair = {
        publicKey: this.publicKey,
        secretKey: this.privateKey
      };

      // Store in localStorage
      localStorage.setItem('chainverdict_private_key', keyData.privateKey);
      localStorage.setItem('chainverdict_public_key', keyData.publicKey);

      console.log('🔑 Successfully imported keys from backup');
      return true;
    } catch (error) {
      console.error('❌ Failed to import keys:', error);
      throw error;
    }
  }

  /**
   * Clear all encryption keys (logout)
   */
  clearKeys() {
    this.keyPair = null;
    this.publicKey = null;
    this.privateKey = null;
    localStorage.removeItem('chainverdict_private_key');
    localStorage.removeItem('chainverdict_public_key');
    console.log('🔑 Cleared encryption keys');
  }

  /**
   * Check if encryption is properly initialized
   */
  isInitialized() {
    return !!(this.publicKey && this.privateKey);
  }

  /**
   * Generate a secure random string for testing
   */
  generateTestMessage() {
    const randomBytes = nacl.randomBytes(32);
    return encodeBase64(randomBytes);
  }
}

// Create singleton instance
const encryptionService = new EncryptionService();

export default encryptionService;
