{"timestamp": "2025-07-25T20:28:10.640Z", "platform": "ChainVerdict Legal Platform", "testType": "VAPT (Vulnerability Assessment & Penetration Testing)", "summary": {"totalTests": 6, "criticalIssues": 2, "vulnerabilities": 4, "warnings": 0, "recommendations": 3}, "riskLevel": "CRITICAL", "findings": {"critical": [{"test": "Frontend Dependency Vulnerability Scan", "output": "{\n  \"auditReportVersion\": 2,\n  \"vulnerabilities\": {},\n  \"metadata\": {\n    \"vulnerabilities\": {\n      \"info\": 0,\n      \"low\": 0,\n      \"moderate\": 0,\n      \"high\": 0,\n      \"critical\": 0,\n      \"total\": 0\n    },\n    \"dependencies\": {\n      \"prod\": 237,\n      \"dev\": 406,\n      \"optional\": 70,\n      \"peer\": 3,\n      \"peerOptional\": 0,\n      \"total\": 712\n    }\n  }\n}\n", "severity": "HIGH"}, {"test": "Backend Dependency Vulnerability Scan", "output": "{\n  \"auditReportVersion\": 2,\n  \"vulnerabilities\": {},\n  \"metadata\": {\n    \"vulnerabilities\": {\n      \"info\": 0,\n      \"low\": 0,\n      \"moderate\": 0,\n      \"high\": 0,\n      \"critical\": 0,\n      \"total\": 0\n    },\n    \"dependencies\": {\n      \"prod\": 167,\n      \"dev\": 30,\n      \"optional\": 2,\n      \"peer\": 4,\n      \"peerOptional\": 0,\n      \"total\": 199\n    }\n  }\n}\n", "severity": "HIGH"}], "vulnerabilities": [{"test": "Outdated Package Detection", "error": "Command failed: cd frontend && npm outdated", "severity": "MEDIUM"}, {"test": "Snyk Advanced Vulnerability Scan", "error": "Command failed: cd frontend && npx snyk test --severity-threshold=medium", "severity": "MEDIUM"}, {"test": "ESLint Security Analysis", "error": "Command failed: cd frontend && npx eslint . --ext .js,.jsx,.ts,.tsx -c ../.eslintrc.security.js --format json\n\nOops! Something went wrong! :(\n\nESLint: 9.31.0\n\nError: ENOENT: no such file or directory, stat 'C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\.eslintrc.security.js'\n    at async Object.stat (node:internal/fs/promises:1036:18)\n    at async loadConfigFile (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\config\\config-loader.js:206:17)\n    at async ConfigLoader.calculateConfigArray (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\config\\config-loader.js:589:23)\n    at async #calculateConfigArray (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\config\\config-loader.js:743:23)\n    at async entryFilter (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\eslint\\eslint-helpers.js:289:5)\n    at async NodeHfs.<anonymous> (file:///C:/Users/<USER>/Desktop/cv-pvt-2/node_modules/@humanfs/core/src/hfs.js:574:24)\n    at async NodeHfs.walk (file:///C:/Users/<USER>/Desktop/cv-pvt-2/node_modules/@humanfs/core/src/hfs.js:614:3)\n    at async globSearch (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\eslint\\eslint-helpers.js:331:20)\n    at async Promise.allSettled (index 0)\n    at async globMultiSearch (C:\\Users\\<USER>\\Desktop\\cv-pvt-2\\node_modules\\eslint\\lib\\eslint\\eslint-helpers.js:422:18)\n", "severity": "MEDIUM"}, {"test": "Rate Limiting", "issue": "No rate limiting detected", "severity": "MEDIUM", "recommendation": "Implement API rate limiting"}], "warnings": [], "recommendations": [{"test": "API Authorization", "recommendation": "Implement role-based access control testing"}, {"test": "Client Data Protection", "recommendation": "Ensure client data is properly encrypted and access-controlled"}, {"test": "Audit Trail", "recommendation": "Implement comprehensive audit logging for all user actions"}]}, "nextSteps": ["URGENT: Address critical security issues within 24 hours", "Review and fix all critical vulnerabilities immediately", "Implement missing security headers", "Set up automated security scanning in CI/CD pipeline", "Conduct regular penetration testing", "Implement comprehensive logging and monitoring", "Train development team on secure coding practices"]}