{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "security:audit": "npm audit --audit-level moderate", "security:lint": "eslint . --config ../eslint.security.config.js", "security:test": "node ../security-test.js", "security:full": "npm run security:audit && npm run security:lint && npm run security:test", "vapt": "node ../vapt-setup.js", "vapt:simple": "node ../simple-vapt.js", "vapt:quick": "npm run security:audit && node ../simple-vapt.js", "vapt:full": "npm run vapt && npm run security:full"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.7.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.11", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "date-fns": "^4.1.0", "framer-motion": "^12.20.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-feather": "^2.0.10", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "react-webcam": "^7.2.0", "recharts": "^3.0.2", "remark-gfm": "^4.0.1", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.11", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-security": "^3.0.1", "globals": "^16.2.0", "vite": "^7.0.0", "vite-plugin-pwa": "^1.0.1", "workbox-window": "^7.3.0"}}