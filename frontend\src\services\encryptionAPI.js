import api from './api';

class EncryptionAPI {
  /**
   * Update user's public key on the server
   * @param {string} publicKey - Base64 encoded public key
   * @returns {Promise<Object>} API response
   */
  async updatePublicKey(publicKey) {
    try {
      const response = await api.put('/encryption/public-key', {
        publicKey
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to update public key:', error);
      throw error;
    }
  }

  /**
   * Get public key for a specific user
   * @param {string} userId - User ID to get public key for
   * @returns {Promise<Object>} User's public key data
   */
  async getPublicKey(userId) {
    try {
      const response = await api.get(`/encryption/public-key/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`❌ Failed to get public key for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get public keys for multiple users
   * @param {Array<string>} userIds - Array of user IDs
   * @returns {Promise<Object>} Multiple users' public key data
   */
  async getMultiplePublicKeys(userIds) {
    try {
      const response = await api.post('/encryption/public-keys', {
        userIds
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get multiple public keys:', error);
      throw error;
    }
  }

  /**
   * Get current user's encryption status
   * @returns {Promise<Object>} Encryption status
   */
  async getEncryptionStatus() {
    try {
      const response = await api.get('/encryption/status');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get encryption status:', error);
      throw error;
    }
  }

  /**
   * Remove user's public key (disable encryption)
   * @returns {Promise<Object>} API response
   */
  async removePublicKey() {
    try {
      const response = await api.delete('/encryption/public-key');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to remove public key:', error);
      throw error;
    }
  }
}

// Create singleton instance
const encryptionAPI = new EncryptionAPI();

export default encryptionAPI;
