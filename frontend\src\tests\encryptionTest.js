// Simple test for encryption service
import encryptionService from '../services/encryptionService.js';

async function testEncryption() {
  console.log('🧪 Testing End-to-End Encryption Service...');
  
  try {
    // Test 1: Initialize encryption service
    console.log('\n1. Testing encryption initialization...');
    const initResult = await encryptionService.initialize();
    console.log('   Result:', initResult);
    
    if (!initResult.success) {
      throw new Error('Failed to initialize encryption');
    }
    
    // Test 2: Generate a second key pair for testing
    console.log('\n2. Generating second key pair for testing...');
    const originalKeys = {
      publicKey: encryptionService.getPublicKey(),
      privateKey: localStorage.getItem('chainverdict_private_key')
    };
    
    // Generate second key pair
    await encryptionService.generateKeyPair();
    const recipientPublicKey = encryptionService.getPublicKey();
    
    // Restore original keys
    localStorage.setItem('chainverdict_private_key', originalKeys.privateKey);
    localStorage.setItem('chainverdict_public_key', originalKeys.publicKey);
    await encryptionService.initialize();
    
    console.log('   Sender public key:', originalKeys.publicKey.substring(0, 20) + '...');
    console.log('   Recipient public key:', recipientPublicKey.substring(0, 20) + '...');
    
    // Test 3: Encrypt a message
    console.log('\n3. Testing message encryption...');
    const testMessage = 'This is a confidential legal message that should be encrypted! 🔐';
    console.log('   Original message:', testMessage);
    
    const encryptedData = encryptionService.encryptMessage(testMessage, recipientPublicKey);
    console.log('   Encrypted data:', {
      encrypted: encryptedData.encrypted.substring(0, 20) + '...',
      nonce: encryptedData.nonce.substring(0, 20) + '...',
      senderPublicKey: encryptedData.senderPublicKey.substring(0, 20) + '...'
    });
    
    // Test 4: Decrypt the message (simulate recipient)
    console.log('\n4. Testing message decryption...');
    
    // Temporarily switch to recipient keys
    localStorage.setItem('chainverdict_private_key', localStorage.getItem('temp_recipient_private_key') || '');
    localStorage.setItem('chainverdict_public_key', recipientPublicKey);
    await encryptionService.initialize();
    
    const decryptedMessage = encryptionService.decryptMessage(encryptedData, originalKeys.publicKey);
    console.log('   Decrypted message:', decryptedMessage);
    
    // Restore original keys
    localStorage.setItem('chainverdict_private_key', originalKeys.privateKey);
    localStorage.setItem('chainverdict_public_key', originalKeys.publicKey);
    await encryptionService.initialize();
    
    // Test 5: Verify message integrity
    console.log('\n5. Testing message integrity...');
    if (decryptedMessage === testMessage) {
      console.log('   ✅ Message integrity verified - encryption/decryption successful!');
    } else {
      throw new Error('Message integrity failed - decrypted message does not match original');
    }
    
    // Test 6: Test key export/import
    console.log('\n6. Testing key backup and restore...');
    const password = 'test-backup-password-123';
    const backupData = encryptionService.exportKeys(password);
    console.log('   Backup data length:', backupData.length);
    
    // Clear keys and restore from backup
    encryptionService.clearKeys();
    const importResult = encryptionService.importKeys(backupData, password);
    console.log('   Import result:', importResult);
    
    // Verify keys were restored correctly
    const restoredPublicKey = encryptionService.getPublicKey();
    if (restoredPublicKey === originalKeys.publicKey) {
      console.log('   ✅ Key backup and restore successful!');
    } else {
      throw new Error('Key restore failed - public key mismatch');
    }
    
    console.log('\n🎉 All encryption tests passed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Encryption service initialization');
    console.log('   ✅ Key pair generation');
    console.log('   ✅ Message encryption');
    console.log('   ✅ Message decryption');
    console.log('   ✅ Message integrity verification');
    console.log('   ✅ Key backup and restore');
    
    return true;
    
  } catch (error) {
    console.error('\n❌ Encryption test failed:', error);
    return false;
  }
}

// Export for use in browser console or testing framework
if (typeof window !== 'undefined') {
  window.testEncryption = testEncryption;
}

export default testEncryption;
