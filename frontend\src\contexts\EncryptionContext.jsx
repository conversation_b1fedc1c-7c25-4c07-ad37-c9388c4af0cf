import React, { createContext, useContext, useState, useEffect } from 'react';
import encryptionService from '../services/encryptionService';
import encryptionAPI from '../services/encryptionAPI';
import { useAuth } from './AuthContext';
import { useToast } from './ToastContext';

const EncryptionContext = createContext();

export const useEncryption = () => {
  const context = useContext(EncryptionContext);
  if (!context) {
    console.error('❌ useEncryption called outside of EncryptionProvider');
    throw new Error('useEncryption must be used within an EncryptionProvider');
  }
  return context;
};

export const EncryptionProvider = ({ children }) => {
  console.log('🔐 EncryptionProvider rendering...');

  const [isInitialized, setIsInitialized] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [publicKeyCache, setPublicKeyCache] = useState(new Map());

  const { user } = useAuth();
  const { success, error } = useToast();

  // Debug state changes
  useEffect(() => {
    console.log('🔐 ENCRYPTION STATE UPDATE:', {
      isInitialized,
      isEnabled,
      isLoading,
      hasUser: !!user,
      userEmail: user?.email
    });

    // Make encryption state available globally for debugging
    window.encryptionEnabled = isEnabled;
    window.encryptionInitialized = isInitialized;
    window.encryptionLoading = isLoading;
  }, [isInitialized, isEnabled, isLoading, user]);

  /**
   * Initialize encryption for the current user
   */
  const initializeEncryption = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      console.log('🔑 Initializing encryption for user:', user.email);

      // Initialize the encryption service
      const result = await encryptionService.initialize();

      if (result.success) {
        // Check if user has encryption enabled on server
        const statusResponse = await encryptionAPI.getEncryptionStatus();

        if (statusResponse.success && statusResponse.data.hasEncryption) {
          // User already has encryption set up
          setIsEnabled(true);
          console.log('🔑 Encryption already enabled for user');
        } else {
          // Upload public key to server
          await encryptionAPI.updatePublicKey(result.publicKey);
          setIsEnabled(true);
          console.log('🔑 Encryption enabled and public key uploaded');
          success('End-to-end encryption enabled for secure messaging');
        }

        setIsInitialized(true);
      } else {
        console.error('❌ Failed to initialize encryption:', result.error);
        error('Failed to initialize encryption');
      }
    } catch (err) {
      console.error('❌ Encryption initialization error:', err);
      error('Failed to set up encryption');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Disable encryption for the current user
   */
  const disableEncryption = async () => {
    setIsLoading(true);
    try {
      // Remove public key from server
      await encryptionAPI.removePublicKey();

      // Clear local encryption keys
      encryptionService.clearKeys();

      // Clear cache
      setPublicKeyCache(new Map());

      setIsEnabled(false);
      setIsInitialized(false);

      success('Encryption disabled successfully');
      console.log('🔑 Encryption disabled for user');
    } catch (err) {
      console.error('❌ Failed to disable encryption:', err);
      error('Failed to disable encryption');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get public key for a user (with caching)
   */
  const getPublicKey = async (userId) => {
    try {
      // Check cache first
      if (publicKeyCache.has(userId)) {
        return publicKeyCache.get(userId);
      }

      // Fetch from server
      const response = await encryptionAPI.getPublicKey(userId);
      if (response.success && response.data.publicKey) {
        // Cache the result
        publicKeyCache.set(userId, response.data.publicKey);
        setPublicKeyCache(new Map(publicKeyCache));

        return response.data.publicKey;
      }

      return null;
    } catch (err) {
      console.error(`❌ Failed to get public key for user ${userId}:`, err);
      return null;
    }
  };

  /**
   * Encrypt a message for a specific recipient
   */
  const encryptMessage = async (message, recipientUserId) => {
    try {
      if (!isEnabled || !encryptionService.isInitialized()) {
        throw new Error('Encryption not enabled or initialized');
      }

      // Get recipient's public key
      const recipientPublicKey = await getPublicKey(recipientUserId);
      if (!recipientPublicKey) {
        throw new Error('Recipient does not have encryption enabled');
      }

      // Encrypt the message
      const encryptedData = encryptionService.encryptMessage(message, recipientPublicKey);

      return {
        success: true,
        encryptedData,
        isEncrypted: true
      };
    } catch (err) {
      console.error('❌ Failed to encrypt message:', err);
      return {
        success: false,
        error: err.message,
        isEncrypted: false
      };
    }
  };

  /**
   * Decrypt a message from a specific sender
   */
  const decryptMessage = async (encryptedData, senderUserId) => {
    try {
      if (!isEnabled || !encryptionService.isInitialized()) {
        throw new Error('Encryption not enabled or initialized');
      }

      // Get sender's public key
      const senderPublicKey = await getPublicKey(senderUserId);
      if (!senderPublicKey) {
        throw new Error('Sender does not have encryption enabled');
      }

      // Decrypt the message
      const decryptedMessage = encryptionService.decryptMessage(encryptedData, senderPublicKey);

      return {
        success: true,
        message: decryptedMessage
      };
    } catch (err) {
      console.error('❌ Failed to decrypt message:', err);
      return {
        success: false,
        error: err.message,
        message: '[Encrypted message - unable to decrypt]'
      };
    }
  };

  /**
   * Export encryption keys for backup
   */
  const exportKeys = (password) => {
    try {
      if (!isEnabled || !encryptionService.isInitialized()) {
        throw new Error('Encryption not enabled or initialized');
      }

      const backupData = encryptionService.exportKeys(password);
      return {
        success: true,
        backupData
      };
    } catch (err) {
      console.error('❌ Failed to export keys:', err);
      return {
        success: false,
        error: err.message
      };
    }
  };

  /**
   * Import encryption keys from backup
   */
  const importKeys = async (backupData, password) => {
    try {
      // Import keys locally
      encryptionService.importKeys(backupData, password);

      // Upload public key to server
      const publicKey = encryptionService.getPublicKey();
      await encryptionAPI.updatePublicKey(publicKey);

      setIsEnabled(true);
      setIsInitialized(true);

      success('Encryption keys imported successfully');
      return { success: true };
    } catch (err) {
      console.error('❌ Failed to import keys:', err);
      error('Failed to import encryption keys');
      return {
        success: false,
        error: err.message
      };
    }
  };

  // Initialize encryption when user logs in
  useEffect(() => {
    console.log('🔐 ENCRYPTION CONTEXT: useEffect triggered', {
      hasUser: !!user,
      isInitialized,
      isLoading,
      userEmail: user?.email
    });

    if (user && !isInitialized && !isLoading) {
      console.log('🔐 ENCRYPTION CONTEXT: Starting initialization...');
      initializeEncryption();
    }
  }, [user, isInitialized, isLoading]);

  // Clear encryption when user logs out
  useEffect(() => {
    if (!user) {
      encryptionService.clearKeys();
      setPublicKeyCache(new Map());
      setIsEnabled(false);
      setIsInitialized(false);
    }
  }, [user]);

  const value = {
    isInitialized,
    isEnabled,
    encryptionEnabled: isEnabled, // Add alias for backward compatibility
    isLoading,
    initializeEncryption,
    disableEncryption,
    encryptMessage,
    decryptMessage,
    getPublicKey,
    exportKeys,
    importKeys,
  };

  // Make functions globally available for debugging
  useEffect(() => {
    window.encryptMessage = encryptMessage;
    window.decryptMessage = decryptMessage;
    console.log('🔐 ENCRYPTION FUNCTIONS UPDATED:', {
      hasEncryptMessage: !!encryptMessage,
      hasDecryptMessage: !!decryptMessage
    });
  }, [encryptMessage, decryptMessage]);

  return (
    <EncryptionContext.Provider value={value}>
      {children}
    </EncryptionContext.Provider>
  );
};
