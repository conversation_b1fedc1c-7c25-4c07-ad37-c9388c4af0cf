import express from "express";
import { body } from "express-validator";
import { protect } from "../middleware/auth.js";
import {
    updateP<PERSON><PERSON><PERSON><PERSON>,
    getP<PERSON><PERSON><PERSON><PERSON>,
    getMultiplePublic<PERSON><PERSON><PERSON>,
    getEncryptionStatus,
    removePub<PERSON><PERSON><PERSON>,
} from "../controllers/encryptionController.js";

const router = express.Router();

// All encryption routes require authentication
router.use(protect);

/**
 * @route   PUT /api/encryption/public-key
 * @desc    Update user's public key for end-to-end encryption
 * @access  Private
 */
router.put(
    "/public-key",
    [
        body("publicKey")
            .notEmpty()
            .withMessage("Public key is required")
            .isBase64()
            .withMessage("Public key must be valid base64")
            .isLength({ min: 44, max: 44 })
            .withMessage("Public key must be exactly 44 characters (32 bytes base64)"),
    ],
    updatePublicKey
);

/**
 * @route   GET /api/encryption/public-key/:userId
 * @desc    Get public key for a specific user
 * @access  Private
 */
router.get("/public-key/:userId", getP<PERSON><PERSON><PERSON><PERSON>);

/**
 * @route   POST /api/encryption/public-keys
 * @desc    Get public keys for multiple users
 * @access  Private
 */
router.post(
    "/public-keys",
    [
        body("userIds")
            .isArray({ min: 1, max: 50 })
            .withMessage("userIds must be an array with 1-50 user IDs")
            .custom((userIds) => {
                // Validate each userId is a valid MongoDB ObjectId format
                const objectIdRegex = /^[0-9a-fA-F]{24}$/;
                for (const userId of userIds) {
                    if (!objectIdRegex.test(userId)) {
                        throw new Error(`Invalid user ID format: ${userId}`);
                    }
                }
                return true;
            }),
    ],
    getMultiplePublicKeys
);

/**
 * @route   GET /api/encryption/status
 * @desc    Get current user's encryption status
 * @access  Private
 */
router.get("/status", getEncryptionStatus);

/**
 * @route   DELETE /api/encryption/public-key
 * @desc    Remove user's public key (disable encryption)
 * @access  Private
 */
router.delete("/public-key", removePublicKey);

export default router;
