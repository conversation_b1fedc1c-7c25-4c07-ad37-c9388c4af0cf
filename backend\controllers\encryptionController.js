import User from "../models/User.js";
import { validationResult } from "express-validator";

/**
 * Update user's public key for end-to-end encryption
 * @route PUT /api/encryption/public-key
 * @access Private
 */
export const updatePublicKey = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                errors: errors.array(),
            });
        }

        const { publicKey } = req.body;
        const userId = req.user.id;

        // Validate public key format (base64 encoded, 32 bytes when decoded)
        if (!publicKey || typeof publicKey !== 'string') {
            return res.status(400).json({
                success: false,
                message: "Invalid public key format",
            });
        }

        // Basic validation - check if it's valid base64
        try {
            const decoded = Buffer.from(publicKey, 'base64');
            if (decoded.length !== 32) {
                throw new Error('Invalid key length');
            }
        } catch (error) {
            return res.status(400).json({
                success: false,
                message: "Invalid public key - must be 32-byte base64 encoded key",
            });
        }

        // Update user's public key
        const user = await User.findByIdAndUpdate(
            userId,
            { publicKey },
            { new: true, select: 'name email role publicKey' }
        );

        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }

        console.log(`🔑 Updated public key for user: ${user.email}`);

        res.status(200).json({
            success: true,
            message: "Public key updated successfully",
            data: {
                userId: user._id,
                publicKey: user.publicKey,
            },
        });
    } catch (error) {
        console.error("❌ Error updating public key:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
};

/**
 * Get public key for a specific user (for encryption)
 * @route GET /api/encryption/public-key/:userId
 * @access Private
 */
export const getPublicKey = async (req, res) => {
    try {
        const { userId } = req.params;
        const requesterId = req.user.id;

        // Find the user and their public key
        const user = await User.findById(userId).select('name email role publicKey');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }

        if (!user.publicKey) {
            return res.status(404).json({
                success: false,
                message: "User has not set up encryption yet",
            });
        }

        // Log the request for security auditing
        console.log(`🔑 Public key requested for ${user.email} by user ${requesterId}`);

        res.status(200).json({
            success: true,
            data: {
                userId: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                publicKey: user.publicKey,
            },
        });
    } catch (error) {
        console.error("❌ Error fetching public key:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
};

/**
 * Get public keys for multiple users (for group chats)
 * @route POST /api/encryption/public-keys
 * @access Private
 */
export const getMultiplePublicKeys = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: "Validation failed",
                errors: errors.array(),
            });
        }

        const { userIds } = req.body;
        const requesterId = req.user.id;

        if (!Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: "userIds must be a non-empty array",
            });
        }

        // Limit to prevent abuse
        if (userIds.length > 50) {
            return res.status(400).json({
                success: false,
                message: "Cannot request more than 50 public keys at once",
            });
        }

        // Find users and their public keys
        const users = await User.find({
            _id: { $in: userIds }
        }).select('name email role publicKey');

        const publicKeys = users.map(user => ({
            userId: user._id,
            name: user.name,
            email: user.email,
            role: user.role,
            publicKey: user.publicKey,
            hasEncryption: !!user.publicKey
        }));

        // Log the request for security auditing
        console.log(`🔑 Multiple public keys requested by user ${requesterId} for ${userIds.length} users`);

        res.status(200).json({
            success: true,
            data: publicKeys,
        });
    } catch (error) {
        console.error("❌ Error fetching multiple public keys:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
};

/**
 * Get current user's encryption status
 * @route GET /api/encryption/status
 * @access Private
 */
export const getEncryptionStatus = async (req, res) => {
    try {
        const userId = req.user.id;

        const user = await User.findById(userId).select('publicKey');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }

        res.status(200).json({
            success: true,
            data: {
                hasEncryption: !!user.publicKey,
                publicKey: user.publicKey,
            },
        });
    } catch (error) {
        console.error("❌ Error checking encryption status:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
};

/**
 * Remove user's public key (disable encryption)
 * @route DELETE /api/encryption/public-key
 * @access Private
 */
export const removePublicKey = async (req, res) => {
    try {
        const userId = req.user.id;

        const user = await User.findByIdAndUpdate(
            userId,
            { $unset: { publicKey: 1 } },
            { new: true, select: 'name email role' }
        );

        if (!user) {
            return res.status(404).json({
                success: false,
                message: "User not found",
            });
        }

        console.log(`🔑 Removed public key for user: ${user.email}`);

        res.status(200).json({
            success: true,
            message: "Encryption disabled successfully",
        });
    } catch (error) {
        console.error("❌ Error removing public key:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
};
