// Debug script for encryption issues
console.log('🔍 Starting encryption debug...');

// Test 1: Check if tweetnacl is available
try {
  const nacl = await import('tweetnacl');
  console.log('✅ tweetnacl imported successfully:', !!nacl.default);
} catch (err) {
  console.error('❌ Failed to import tweetnacl:', err);
}

// Test 2: Check if tweetnacl-util is available
try {
  const naclUtil = await import('tweetnacl-util');
  console.log('✅ tweetnacl-util imported successfully:', !!naclUtil);
} catch (err) {
  console.error('❌ Failed to import tweetnacl-util:', err);
}

// Test 3: Check if encryption service loads
try {
  const encryptionService = await import('../services/encryptionService.js');
  console.log('✅ encryptionService imported successfully:', !!encryptionService.default);
} catch (err) {
  console.error('❌ Failed to import encryptionService:', err);
}

// Test 4: Check if encryption API loads
try {
  const encryptionAPI = await import('../services/encryptionAPI.js');
  console.log('✅ encryptionAPI imported successfully:', !!encryptionAPI.default);
} catch (err) {
  console.error('❌ Failed to import encryptionAPI:', err);
}

// Test 5: Check localStorage
console.log('🔑 LocalStorage check:');
console.log('  Private key exists:', !!localStorage.getItem('chainverdict_private_key'));
console.log('  Public key exists:', !!localStorage.getItem('chainverdict_public_key'));

// Test 6: Simple encryption test
try {
  const nacl = await import('tweetnacl');
  const { encodeBase64, decodeBase64, encodeUTF8, decodeUTF8 } = await import('tweetnacl-util');
  
  console.log('🧪 Testing basic encryption...');
  
  // Generate key pair
  const keyPair = nacl.default.box.keyPair();
  console.log('✅ Key pair generated');
  
  // Test message
  const message = 'Hello encryption test!';
  const messageBytes = encodeUTF8(message);
  const nonce = nacl.default.randomBytes(24);
  
  // Encrypt
  const encrypted = nacl.default.box(messageBytes, nonce, keyPair.publicKey, keyPair.secretKey);
  console.log('✅ Message encrypted');
  
  // Decrypt
  const decrypted = nacl.default.box.open(encrypted, nonce, keyPair.publicKey, keyPair.secretKey);
  const decryptedMessage = decodeUTF8(decrypted);
  
  console.log('✅ Message decrypted:', decryptedMessage);
  console.log('✅ Encryption test successful!');
  
} catch (err) {
  console.error('❌ Basic encryption test failed:', err);
}

console.log('🔍 Encryption debug complete!');

export default true;
